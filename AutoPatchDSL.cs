// AutoPatch DSL Parser - Parse .autopatch files instead of using attributes
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;

public class AutoPatchDSL
{
    public class PatchDefinition
    {
        public string TargetType { get; set; }
        public string TargetMethod { get; set; }
        public string PatchType { get; set; }
        public string EventName { get; set; }
        public List<string> ParameterTypes { get; set; } = new();
        public string SourceLine { get; set; }
        public int LineNumber { get; set; }
    }

    public static List<PatchDefinition> ParseFile(string filePath)
    {
        var patches = new List<PatchDefinition>();
        
        if (!File.Exists(filePath))
            return patches;

        var lines = File.ReadAllLines(filePath);
        
        for (int i = 0; i < lines.Length; i++)
        {
            var line = lines[i].Trim();
            
            // Skip empty lines and comments
            if (string.IsNullOrEmpty(line) || line.StartsWith("#"))
                continue;
                
            try
            {
                var patch = ParseLine(line, i + 1);
                if (patch != null)
                    patches.Add(patch);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error parsing line {i + 1}: {line}\n{ex.Message}");
            }
        }
        
        return patches;
    }

    private static PatchDefinition ParseLine(string line, int lineNumber)
    {
        // Pattern: TargetType.MethodName[(params)] -> PatchType [-> EventName]
        var pattern = @"^([^.]+)\.([^(\s]+)(?:\(([^)]*)\))?\s*->\s*(\w+)(?:\s*->\s*(\w+))?$";
        var match = Regex.Match(line, pattern);
        
        if (!match.Success)
            throw new ArgumentException($"Invalid syntax. Expected: TargetType.MethodName -> PatchType [-> EventName]");
            
        var targetType = match.Groups[1].Value.Trim();
        var methodName = match.Groups[2].Value.Trim();
        var parameters = match.Groups[3].Value.Trim();
        var patchType = match.Groups[4].Value.Trim();
        var eventName = match.Groups[5].Success ? match.Groups[5].Value.Trim() : null;
        
        // Validate patch type
        var validPatchTypes = new[] { "Prefix", "Postfix", "Transpiler", "Finalizer" };
        if (!validPatchTypes.Contains(patchType, StringComparer.OrdinalIgnoreCase))
            throw new ArgumentException($"Invalid patch type '{patchType}'. Valid types: {string.Join(", ", validPatchTypes)}");
        
        var patch = new PatchDefinition
        {
            TargetType = targetType,
            TargetMethod = methodName,
            PatchType = patchType,
            EventName = eventName ?? $"On{methodName}{patchType}",
            SourceLine = line,
            LineNumber = lineNumber
        };
        
        // Parse parameter types if specified
        if (!string.IsNullOrEmpty(parameters))
        {
            patch.ParameterTypes = parameters
                .Split(',')
                .Select(p => p.Trim())
                .Where(p => !string.IsNullOrEmpty(p))
                .ToList();
        }
        
        return patch;
    }

    public static void ValidateDefinitions(List<PatchDefinition> patches)
    {
        var errors = new List<string>();
        
        foreach (var patch in patches)
        {
            // Validate target type format
            if (string.IsNullOrEmpty(patch.TargetType))
                errors.Add($"Line {patch.LineNumber}: Target type cannot be empty");
                
            // Validate method name
            if (string.IsNullOrEmpty(patch.TargetMethod))
                errors.Add($"Line {patch.LineNumber}: Method name cannot be empty");
                
            // Validate event name format
            if (!string.IsNullOrEmpty(patch.EventName) && !IsValidIdentifier(patch.EventName))
                errors.Add($"Line {patch.LineNumber}: Invalid event name '{patch.EventName}'");
        }
        
        // Check for duplicate event names
        var eventGroups = patches
            .Where(p => !string.IsNullOrEmpty(p.EventName))
            .GroupBy(p => p.EventName)
            .Where(g => g.Count() > 1);
            
        foreach (var group in eventGroups)
        {
            var lines = string.Join(", ", group.Select(p => p.LineNumber));
            errors.Add($"Duplicate event name '{group.Key}' found on lines: {lines}");
        }
        
        if (errors.Any())
            throw new InvalidOperationException($"DSL validation errors:\n{string.Join("\n", errors)}");
    }

    private static bool IsValidIdentifier(string name)
    {
        if (string.IsNullOrEmpty(name))
            return false;
            
        // Must start with letter or underscore, followed by letters, digits, or underscores
        return Regex.IsMatch(name, @"^[a-zA-Z_][a-zA-Z0-9_]*$");
    }

    // Generate example DSL file
    public static string GenerateExampleFile()
    {
        return @"# AutoPatch DSL Configuration File
# Define your patches here without cluttering your code with attributes

# Basic syntax: TargetType.MethodName -> PatchType [-> EventName]
# PatchType can be: Prefix, Postfix, Transpiler, Finalizer

# Example patches
Player.TakeDamage -> Prefix -> OnPlayerDamage
Player.Die -> Postfix -> OnPlayerDied
Enemy.Attack -> Prefix -> OnEnemyAttack
GameManager.Update -> Transpiler -> OnGameUpdateModify

# With parameter types for overloaded methods
Weapon.Fire(Vector3) -> Prefix -> OnWeaponFireAtPosition
Weapon.Fire(Transform) -> Prefix -> OnWeaponFireAtTarget

# Multiple patches for same method
Item.Use -> Prefix -> OnItemUsing
Item.Use -> Postfix -> OnItemUsed

# Comments and organization
## Combat System
HealthSystem.Heal -> Postfix -> OnPlayerHealed
HealthSystem.TakeDamage -> Prefix -> OnDamageReceived

## Movement System  
PlayerController.Move -> Prefix -> OnPlayerMove
PlayerController.Jump -> Postfix -> OnPlayerJump";
    }
}
