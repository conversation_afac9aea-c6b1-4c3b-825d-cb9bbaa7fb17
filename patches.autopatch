# AutoPatch DSL Configuration File
# Simple syntax: TargetType.MethodName -> PatchType
# Event names are automatically generated as: On{MethodName}{PatchType}

# Real Last Epoch patches using actual game types
Il2Cpp.MinimapFogOfWar.Initialize -> Postfix         # Creates: OnInitializePostfix
Il2Cpp.GroundItemLabel.initialise -> Postfix         # Creates: OnInitialisePostfix
Il2Cpp.LocalPlayer.Update -> Prefix                  # Creates: OnUpdatePrefix

# Your custom TestLE types
TestLE.Enemy.OnDeath -> Postfix                      # Creates: OnOnDeathPostfix
TestLE.Enemy.RemoveEnemy -> Prefix                    # Creates: OnRemoveEnemyPrefix