# AutoPatch DSL Configuration File
# Define your patches here without cluttering your code with attributes

# Basic syntax: TargetType.MethodName -> PatchType [-> EventName]
# PatchType can be: Prefix, Postfix, Transpiler, Finalizer

# Example patches for MinimapFogOfWar
MinimapFogOfWar.UpdateFogOfWar -> Postfix
MinimapFogOfWar.ClearFog -> Prefix -> OnClearFogRequested
MinimapFogOfWar.RevealArea -> Postfix -> OnAreaRevealed

# You can also specify parameter types for overloaded methods
Player.TakeDamage(float) -> Prefix -> OnPlayerDamage
Player.TakeDamage(float, DamageType) -> Prefix -> OnPlayerDamageWithType

# Multiple patches for the same method
Enemy.Die -> Prefix -> OnEnemyDying
Enemy.Die -> Postfix -> OnEnemyDied

# Transpiler example
GameManager.Update -> Transpiler -> OnGameUpdateTranspile

# Comments and empty lines are ignored
# You can organize patches by feature:

## Movement System
PlayerController.Move -> Prefix
PlayerController.Jump -> Postfix -> OnPlayerJumped

## Combat System  
WeaponSystem.Fire -> Prefix -> OnWeaponFire
WeaponSystem.Reload -> Postfix -> OnWeaponReloaded

## UI System
UIManager.ShowDialog -> Prefix -> OnDialogShowing
UIManager.HideDialog -> Postfix -> OnDialogHidden

# Advanced syntax with parameter filtering
# Only patch methods with specific parameter signatures
InventoryManager.AddItem(Item, int) -> Postfix -> OnItemAdded
InventoryManager.RemoveItem(string) -> Prefix -> OnItemRemoving

# Conditional patches (future feature)
# GameSettings.SetDifficulty -> Prefix -> OnDifficultyChange [when: Debug]
