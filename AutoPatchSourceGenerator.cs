// AutoPatchSourceGenerator.cs - Clean, comprehensive Harmony patch generator
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.CodeAnalysis.Text;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Linq;
using System.Text;
using System;

[Generator]
public class AutoPatchSourceGenerator : IIncrementalGenerator
{
    public void Initialize(IncrementalGeneratorInitializationContext context)
    {
        var autoPatchMethods = context.SyntaxProvider
            .CreateSyntaxProvider(
                predicate: static (s, _) => IsPotentialAutoPatchMethod(s),
                transform: static (ctx, _) => GetAutoPatchMethodInfo(ctx))
            .Where(static m => m is not null);

        var compilationAndMethods = context.CompilationProvider.Combine(autoPatchMethods.Collect());
        context.RegisterSourceOutput(compilationAndMethods, static (spc, source) => Execute(spc, source.Left, source.Right));
    }

    private static bool IsPotentialAutoPatchMethod(SyntaxNode node) =>
        node is MethodDeclarationSyntax method &&
        method.AttributeLists.Count > 0 &&
        method.AttributeLists
            .SelectMany(al => al.Attributes)
            .Any(attr => attr.Name.ToString().Contains("AutoPatch"));

    private static MethodDeclarationSyntax? GetAutoPatchMethodInfo(GeneratorSyntaxContext context)
    {
        var method = (MethodDeclarationSyntax)context.Node;
        var semanticModel = context.SemanticModel;

        // More flexible attribute detection - check for any attribute containing "AutoPatch"
        var hasAutoPatchAttribute = method.AttributeLists
            .SelectMany(al => al.Attributes)
            .Any(attr =>
            {
                var attrName = attr.Name.ToString();
                if (attrName.Contains("AutoPatch"))
                    return true;
                    
                // Also check semantic model if available
                var symbolInfo = semanticModel.GetSymbolInfo(attr);
                var typeName = symbolInfo.Symbol?.ContainingType?.Name;
                return typeName == "AutoPatchAttribute" || typeName == "AutoPatch";
            });

        return hasAutoPatchAttribute ? method : null;
    }

    private static void Execute(SourceProductionContext context, Compilation compilation, ImmutableArray<MethodDeclarationSyntax?> methods)
    {
        // Always generate a debug file to see what's happening
        var debugInfo = new StringBuilder();
        debugInfo.AppendLine("// AutoPatch Debug Info");
        debugInfo.AppendLine($"// Found {methods.Length} potential methods");
        debugInfo.AppendLine($"// Compilation: {compilation.AssemblyName}");
        
        if (methods.IsDefaultOrEmpty)
        {
            debugInfo.AppendLine("// No methods found with AutoPatch attributes");
            context.AddSource("AutoPatchDebug.g.cs", SourceText.From(debugInfo.ToString(), Encoding.UTF8));
            return;
        }

        var validPatches = new List<PatchDefinition>();
        var validator = new PatchValidator(compilation);

        foreach (var method in methods.Where(m => m != null))
        {
            debugInfo.AppendLine($"// Processing method: {method.Identifier.ValueText}");
            
            var semanticModel = compilation.GetSemanticModel(method.SyntaxTree);
            var methodSymbol = semanticModel.GetDeclaredSymbol(method);
            if (methodSymbol == null) 
            {
                debugInfo.AppendLine($"//   - Could not get method symbol");
                continue;
            }

            var patchDef = AnalyzePatchMethod(methodSymbol, method, validator);
            debugInfo.AppendLine($"//   - Target: {patchDef.TargetType}.{patchDef.TargetMethod}");
            debugInfo.AppendLine($"//   - Valid: {patchDef.IsValid}");
            
            if (patchDef.IsValid)
            {
                validPatches.Add(patchDef);
                ReportSuccess(context, method, patchDef);
            }
            else
            {
                debugInfo.AppendLine($"//   - Error: {patchDef.ErrorMessage}");
                ReportError(context, method, patchDef.ErrorMessage);
            }
        }

        debugInfo.AppendLine($"// Total valid patches: {validPatches.Count}");
        context.AddSource("AutoPatchDebug.g.cs", SourceText.From(debugInfo.ToString(), Encoding.UTF8));

        if (validPatches.Any())
        {
            GenerateSourceFiles(context, validPatches);
        }
    }

    private static PatchDefinition AnalyzePatchMethod(IMethodSymbol methodSymbol, MethodDeclarationSyntax methodSyntax, PatchValidator validator)
    {
        var patchDef = new PatchDefinition
        {
            OriginalMethodName = methodSymbol.Name,
            ContainingClass = methodSymbol.ContainingType.ToDisplayString(),
            ReturnType = methodSymbol.ReturnType.ToDisplayString(),
            Parameters = methodSymbol.Parameters.ToList(),
            PatchType = DeterminePatchType(methodSymbol)
        };

        ExtractTargetInfo(patchDef, methodSyntax, methodSymbol);
        
        if (!string.IsNullOrEmpty(patchDef.TargetType) && !string.IsNullOrEmpty(patchDef.TargetMethod))
        {
            var validationResult = validator.ValidateTargetMethod(patchDef.TargetType, patchDef.TargetMethod, methodSymbol);
            patchDef.IsValid = validationResult.IsValid;
            patchDef.ErrorMessage = validationResult.IsValid ? null : string.Join("\n", validationResult.Errors);
            patchDef.TargetSignature = validationResult.TargetMethod?.ToDisplayString() ?? $"{patchDef.TargetType}.{patchDef.TargetMethod}";
        }
        else
        {
            patchDef.IsValid = false;
            patchDef.ErrorMessage = "Target type and method must be specified or auto-detectable";
        }

        return patchDef;
    }

    private static void ExtractTargetInfo(PatchDefinition patchDef, MethodDeclarationSyntax methodSyntax, IMethodSymbol methodSymbol)
    {
        var autoPatchAttr = methodSyntax.AttributeLists
            .SelectMany(al => al.Attributes)
            .FirstOrDefault(attr => attr.Name.ToString().Contains("AutoPatch"));

        if (autoPatchAttr?.ArgumentList?.Arguments.Count > 0)
        {
            var args = autoPatchAttr.ArgumentList.Arguments;
            if (args.Count >= 2)
            {
                patchDef.TargetType = ExtractStringFromAttributeArgument(args[0]);
                patchDef.TargetMethod = ExtractStringFromAttributeArgument(args[1]);
            }
            else if (args.Count == 1)
            {
                var fullTarget = ExtractStringFromAttributeArgument(args[0]);
                var lastDot = fullTarget.LastIndexOf('.');
                if (lastDot > 0)
                {
                    patchDef.TargetType = fullTarget.Substring(0, lastDot);
                    patchDef.TargetMethod = fullTarget.Substring(lastDot + 1);
                }
            }
        }
        else
        {
            AutoDetectTarget(patchDef, methodSymbol);
        }
    }

    private static void AutoDetectTarget(PatchDefinition patchDef, IMethodSymbol methodSymbol)
    {
        var methodName = methodSymbol.Name;
        var cleanName = RemovePatchSuffixFromMethodName(methodName);

        patchDef.TargetMethod = cleanName;

        var instanceParam = methodSymbol.Parameters.FirstOrDefault(p => p.Name == "__instance");
        if (instanceParam != null)
        {
            patchDef.TargetType = instanceParam.Type.ToDisplayString();
        }
        else
        {
            var firstParam = methodSymbol.Parameters.FirstOrDefault();
            if (firstParam != null && !IsHarmonyParameter(firstParam.Name))
            {
                patchDef.TargetType = firstParam.Type.ToDisplayString();
            }
        }
    }

    private static PatchType DeterminePatchType(IMethodSymbol methodSymbol)
    {
        var methodName = methodSymbol.Name.ToLower();
        var returnType = methodSymbol.ReturnType.Name;

        if (methodName.Contains("transpiler")) return PatchType.Transpiler;
        if (methodName.Contains("post") || methodName.Contains("after")) return PatchType.Postfix;
        if (methodName.Contains("finalizer")) return PatchType.Finalizer;

        return returnType == "Boolean" ? PatchType.Prefix : 
               returnType == "Void" ? PatchType.Postfix : 
               PatchType.Prefix;
    }

    private static void GenerateSourceFiles(SourceProductionContext context, List<PatchDefinition> patches)
    {
        try
        {
            var harmonyPatchSource = GenerateHarmonyPatches(patches);
            context.AddSource("AutoPatches.g.cs", SourceText.From(harmonyPatchSource, Encoding.UTF8));

            var applierSource = GeneratePatchApplier(patches);
            context.AddSource("AutoPatchApplier.g.cs", SourceText.From(applierSource, Encoding.UTF8));
            
            // Add success debug info
            var successDebug = $"// Successfully generated {patches.Count} patches\n// Files: AutoPatches.g.cs, AutoPatchApplier.g.cs";
            context.AddSource("AutoPatchSuccess.g.cs", SourceText.From(successDebug, Encoding.UTF8));
        }
        catch (Exception ex)
        {
            var errorDebug = $"// Generation failed: {ex.Message}\n// Stack trace: {ex.StackTrace}";
            context.AddSource("AutoPatchError.g.cs", SourceText.From(errorDebug, Encoding.UTF8));
        }
    }

    private static string GenerateHarmonyPatches(List<PatchDefinition> patches)
    {
        var sb = new StringBuilder();
        
        AppendHeader(sb, patches);
        
        sb.AppendLine("namespace AutoPatch.Generated");
        sb.AppendLine("{");

        var patchGroups = patches.GroupBy(p => p.ContainingClass).ToList();
        
        foreach (var group in patchGroups)
        {
            GeneratePatchClass(sb, group.Key, group.ToList());
        }

        GenerateMainPatchesClass(sb, patchGroups);
        
        sb.AppendLine("}");
        return sb.ToString();
    }

    private static void AppendHeader(StringBuilder sb, List<PatchDefinition> patches)
    {
        sb.AppendLine("// Auto-generated Harmony patches by AutoPatch Source Generator");
        sb.AppendLine("using System;");
        sb.AppendLine("using System.Collections.Generic;");
        sb.AppendLine("using System.Linq;");
        sb.AppendLine("using System.Reflection;");
        sb.AppendLine("using System.Reflection.Emit;");
        sb.AppendLine("using HarmonyLib;");
        sb.AppendLine("using MelonLoader;");
        
        var userNamespaces = patches
            .Select(p => GetNamespaceFromFullName(p.ContainingClass))
            .Where(ns => !string.IsNullOrEmpty(ns) && !IsSystemNamespace(ns))
            .Distinct();
            
        foreach (var ns in userNamespaces)
        {
            sb.AppendLine($"using {ns};");
        }
        
        sb.AppendLine();
    }

    private static void GeneratePatchClass(StringBuilder sb, string sourceClass, List<PatchDefinition> classPatches)
    {
        var simpleClassName = GetSimpleClassName(sourceClass);
        
        sb.AppendLine($"    // Generated patches for {sourceClass}");
        sb.AppendLine($"    public static class Generated_{simpleClassName}_Patches");
        sb.AppendLine("    {");
        
        foreach (var patch in classPatches)
        {
            sb.AppendLine($"        // Patch: {patch.OriginalMethodName} -> {patch.TargetType}.{patch.TargetMethod}");
            GenerateHarmonyPatchMethod(sb, patch, simpleClassName);
        }
        
        sb.AppendLine("    }");
        sb.AppendLine();
    }

    private static void GenerateHarmonyPatchMethod(StringBuilder sb, PatchDefinition patch, string simpleClassName)
    {
        var cleanMethodName = RemovePatchSuffixFromMethodName(patch.OriginalMethodName);
        var patchMethodName = $"{cleanMethodName}_{patch.PatchType}";
        var harmonyAttributeName = GetHarmonyAttributeName(patch.PatchType);
        var eventName = $"On{cleanMethodName}{patch.PatchType}";

        // Generate the event declaration first
        GenerateEventDeclaration(sb, patch, eventName);

        sb.AppendLine($"        [HarmonyPatch(typeof({patch.TargetType}), \"{patch.TargetMethod}\")]");
        sb.AppendLine($"        [{harmonyAttributeName}]");
        sb.AppendLine($"        public static {patch.ReturnType} {patchMethodName}(");

        var paramStrings = patch.Parameters.Select(p => $"{p.Type.ToDisplayString()} {p.Name}");
        sb.AppendLine($"            {string.Join(",\n            ", paramStrings)})");
        sb.AppendLine("        {");

        // Generate event-based logic instead of direct method calls
        GenerateEventInvocationLogic(sb, patch, eventName);

        sb.AppendLine("        }");
        sb.AppendLine();

        // Remove the complex TargetMethod generation - use simple HarmonyPatch attribute instead
    }

    private static void GenerateEventDeclaration(StringBuilder sb, PatchDefinition patch, string eventName)
    {
        var delegateType = GetDelegateType(patch);
        sb.AppendLine($"        // Event for {patch.OriginalMethodName} -> {patch.TargetType}.{patch.TargetMethod}");
        sb.AppendLine($"        public static event {delegateType} {eventName};");
        sb.AppendLine();
    }

    private static void GenerateEventInvocationLogic(StringBuilder sb, PatchDefinition patch, string eventName)
    {
        var callParams = string.Join(", ", patch.Parameters.Select(p => p.Name));
        var delegateType = GetDelegateType(patch);

        sb.AppendLine($"            if ({eventName} != null)");
        sb.AppendLine("            {");

        switch (patch.PatchType)
        {
            case PatchType.Prefix:
                GeneratePrefixEventLogic(sb, eventName, delegateType, callParams, patch.ReturnType);
                break;
            case PatchType.Postfix:
                GeneratePostfixEventLogic(sb, eventName, delegateType, callParams);
                break;
            case PatchType.Transpiler:
                GenerateTranspilerEventLogic(sb, eventName, delegateType, callParams);
                break;
            case PatchType.Finalizer:
                GenerateFinalizerEventLogic(sb, eventName, delegateType, callParams);
                break;
        }

        sb.AppendLine("            }");

        // Provide default return value if no handlers or for specific patch types
        if (patch.ReturnType != "void")
        {
            var defaultReturn = GetDefaultReturnValue(patch);
            sb.AppendLine($"            return {defaultReturn};");
        }
    }

    private static void GeneratePrefixEventLogic(StringBuilder sb, string eventName, string delegateType, string callParams, string returnType)
    {
        if (returnType == "bool")
        {
            sb.AppendLine("                foreach (var handler in " + eventName + ".GetInvocationList())");
            sb.AppendLine("                {");
            sb.AppendLine($"                    var result = (({delegateType})handler)({callParams});");
            sb.AppendLine("                    if (!result) return false; // Handler cancelled execution");
            sb.AppendLine("                }");
        }
        else
        {
            sb.AppendLine("                foreach (var handler in " + eventName + ".GetInvocationList())");
            sb.AppendLine("                {");
            sb.AppendLine($"                    (({delegateType})handler)({callParams});");
            sb.AppendLine("                }");
        }
    }

    private static void GeneratePostfixEventLogic(StringBuilder sb, string eventName, string delegateType, string callParams)
    {
        sb.AppendLine("                foreach (var handler in " + eventName + ".GetInvocationList())");
        sb.AppendLine("                {");
        sb.AppendLine($"                    (({delegateType})handler)({callParams});");
        sb.AppendLine("                }");
    }

    private static void GenerateTranspilerEventLogic(StringBuilder sb, string eventName, string delegateType, string callParams)
    {
        sb.AppendLine("                foreach (var handler in " + eventName + ".GetInvocationList())");
        sb.AppendLine("                {");
        sb.AppendLine($"                    var result = (({delegateType})handler)({callParams});");
        sb.AppendLine("                    if (result != null) return result; // Return first non-null result");
        sb.AppendLine("                }");
    }

    private static void GenerateFinalizerEventLogic(StringBuilder sb, string eventName, string delegateType, string callParams)
    {
        sb.AppendLine("                foreach (var handler in " + eventName + ".GetInvocationList())");
        sb.AppendLine("                {");
        sb.AppendLine("                    try");
        sb.AppendLine("                    {");
        sb.AppendLine($"                        (({delegateType})handler)({callParams});");
        sb.AppendLine("                    }");
        sb.AppendLine("                    catch (System.Exception ex)");
        sb.AppendLine("                    {");
        sb.AppendLine("                        MelonLoader.MelonLogger.Error($\"[AutoPatch] Exception in finalizer event handler: {ex.Message}\");");
        sb.AppendLine("                    }");
        sb.AppendLine("                }");
    }

    private static string GetDelegateType(PatchDefinition patch)
    {
        var paramTypes = patch.Parameters.Select(p => p.Type.ToDisplayString()).ToList();

        if (patch.ReturnType == "void")
        {
            return paramTypes.Any() ? $"Action<{string.Join(", ", paramTypes)}>" : "Action";
        }
        else
        {
            var allTypes = paramTypes.Concat(new[] { patch.ReturnType });
            return $"Func<{string.Join(", ", allTypes)}>";
        }
    }

    private static string GetDefaultReturnValue(PatchDefinition patch)
    {
        return patch.PatchType switch
        {
            PatchType.Prefix when patch.ReturnType == "bool" => "true", // Allow execution by default
            PatchType.Transpiler => "null", // No transformation by default
            _ => $"default({patch.ReturnType})"
        };
    }

    // Removed complex TargetMethod generation - now using simple HarmonyPatch attributes

    private static void GenerateMainPatchesClass(StringBuilder sb, List<IGrouping<string, PatchDefinition>> patchGroups)
    {
        sb.AppendLine("    public static class GeneratedPatches");
        sb.AppendLine("    {");
        sb.AppendLine("        public static System.Type[] GetAllPatchTypes()");
        sb.AppendLine("        {");
        sb.AppendLine("            return new System.Type[] {");
        
        foreach (var group in patchGroups)
        {
            var simpleClassName = GetSimpleClassName(group.Key);
            sb.AppendLine($"                typeof(Generated_{simpleClassName}_Patches),");
        }
        
        sb.AppendLine("            };");
        sb.AppendLine("        }");
        sb.AppendLine("    }");
    }

    private static string GeneratePatchApplier(List<PatchDefinition> patches)
    {
        var sb = new StringBuilder();
        
        sb.AppendLine("// Auto-generated patch applier");
        sb.AppendLine("using System;");
        sb.AppendLine("using HarmonyLib;");
        sb.AppendLine("using MelonLoader;");
        sb.AppendLine();
        sb.AppendLine("namespace AutoPatch.Generated");
        sb.AppendLine("{");
        sb.AppendLine("    public static class AutoPatchApplier");
        sb.AppendLine("    {");
        sb.AppendLine("        public static void LogPatchInfo()");
        sb.AppendLine("        {");
        sb.AppendLine($"            MelonLogger.Msg(\"[AutoPatch] Generated {patches.Count} auto-patches:\");");
        
        foreach (var patch in patches)
        {
            sb.AppendLine($"            MelonLogger.Msg(\"  ✓ {patch.OriginalMethodName} -> {patch.TargetSignature}\");");
        }
        
        sb.AppendLine("        }");
        sb.AppendLine();
        sb.AppendLine("        public static void ManuallyApplyPatches(HarmonyLib.Harmony harmony = null)");
        sb.AppendLine("        {");
        sb.AppendLine("            try");
        sb.AppendLine("            {");
        sb.AppendLine($"                MelonLogger.Msg(\"[AutoPatch] Starting to apply {patches.Count} patches...\");");
        sb.AppendLine("                harmony ??= new HarmonyLib.Harmony(\"com.autopatch.generated\");");
        sb.AppendLine("                var patchTypes = GeneratedPatches.GetAllPatchTypes();");
        sb.AppendLine($"                MelonLogger.Msg($\"[AutoPatch] Found {{patchTypes.Length}} patch types\");");
        sb.AppendLine("                foreach (var patchType in patchTypes)");
        sb.AppendLine("                {");
        sb.AppendLine("                    MelonLogger.Msg($\"[AutoPatch] Applying patches from {patchType.Name}...\");");
        sb.AppendLine("                    harmony.PatchAll(patchType);");
        sb.AppendLine("                    MelonLogger.Msg($\"[AutoPatch] ✓ Applied patches from {patchType.Name}\");");
        sb.AppendLine("                }");
        sb.AppendLine($"                MelonLogger.Msg(\"[AutoPatch] ✓ Successfully applied all {patches.Count} patches!\");");
        sb.AppendLine("            }");
        sb.AppendLine("            catch (Exception ex)");
        sb.AppendLine("            {");
        sb.AppendLine("                MelonLogger.Error($\"[AutoPatch] ✗ Failed to apply patches: {ex.Message}\");");
        sb.AppendLine("                if (ex.InnerException != null)");
        sb.AppendLine("                    MelonLogger.Error($\"[AutoPatch] Inner exception: {ex.InnerException.Message}\");");
        sb.AppendLine("                MelonLogger.Error($\"[AutoPatch] Stack trace: {ex.StackTrace}\");");
        sb.AppendLine("                throw;");
        sb.AppendLine("            }");
        sb.AppendLine("        }");
        sb.AppendLine("    }");
        sb.AppendLine("}");
        
        return sb.ToString();
    }

    // Validation System
    public class PatchValidator
    {
        private readonly Compilation _compilation;
        private readonly Dictionary<string, INamedTypeSymbol> _typeCache = new();

        public PatchValidator(Compilation compilation) => _compilation = compilation;

        public ValidationResult ValidateTargetMethod(string targetType, string targetMethod, IMethodSymbol patchMethod)
        {
            var result = new ValidationResult();

            var targetTypeSymbol = GetTypeSymbol(targetType);
            if (targetTypeSymbol == null)
            {
                result.AddError($"Target type '{targetType}' not found");
                return result;
            }

            var targetMethods = targetTypeSymbol.GetMembers(targetMethod).OfType<IMethodSymbol>().ToList();
            if (!targetMethods.Any())
            {
                result.AddError($"Target method '{targetMethod}' not found in '{targetType}'");
                return result;
            }

            var compatibleMethod = FindCompatibleTargetMethod(targetMethods, patchMethod);
            if (compatibleMethod == null)
            {
                result.AddError($"No compatible overload found for '{targetMethod}'");
                return result;
            }

            ValidateMethodCompatibility(compatibleMethod, patchMethod, result);
            result.TargetMethod = compatibleMethod;
            return result;
        }

        private INamedTypeSymbol? GetTypeSymbol(string typeName)
        {
            if (_typeCache.TryGetValue(typeName, out var cached))
                return cached;

            var symbol = _compilation.GetTypeByMetadataName(typeName);
            if (symbol != null)
                _typeCache[typeName] = symbol;

            return symbol;
        }

        private IMethodSymbol? FindCompatibleTargetMethod(List<IMethodSymbol> targetMethods, IMethodSymbol patchMethod)
        {
            foreach (var targetMethod in targetMethods)
            {
                if (IsParameterCompatible(targetMethod, patchMethod))
                    return targetMethod;
            }
            return null;
        }

        private bool IsParameterCompatible(IMethodSymbol targetMethod, IMethodSymbol patchMethod)
        {
            var patchParams = patchMethod.Parameters.Where(p => !IsHarmonyParameter(p.Name)).ToList();
            var targetParams = targetMethod.Parameters.ToList();

            if (patchParams.Count > targetParams.Count) return false;

            for (int i = 0; i < Math.Min(patchParams.Count, targetParams.Count); i++)
            {
                if (!SymbolEqualityComparer.Default.Equals(patchParams[i].Type, targetParams[i].Type))
                    return false;
            }

            return true;
        }

        private void ValidateMethodCompatibility(IMethodSymbol targetMethod, IMethodSymbol patchMethod, ValidationResult result)
        {
            var patchType = DeterminePatchType(patchMethod);
            var patchReturnType = patchMethod.ReturnType;

            switch (patchType)
            {
                case PatchType.Prefix when !patchReturnType.SpecialType.Equals(SpecialType.System_Boolean) &&
                                          !patchReturnType.SpecialType.Equals(SpecialType.System_Void):
                    result.AddError($"Prefix patches should return bool or void, got '{patchReturnType}'");
                    break;

                case PatchType.Transpiler when !patchReturnType.ToDisplayString().Contains("IEnumerable"):
                    result.AddError($"Transpiler patches should return IEnumerable<CodeInstruction>");
                    break;
            }
        }
    }

    public class ValidationResult
    {
        public List<string> Errors { get; } = new();
        public IMethodSymbol? TargetMethod { get; set; }
        public bool IsValid => !Errors.Any();
        public void AddError(string error) => Errors.Add(error);
    }

    // Data Models
    private class PatchDefinition
    {
        public string OriginalMethodName { get; set; }
        public string ContainingClass { get; set; }
        public string TargetType { get; set; }
        public string TargetMethod { get; set; }
        public string TargetSignature { get; set; }
        public PatchType PatchType { get; set; }
        public string ReturnType { get; set; }
        public List<IParameterSymbol> Parameters { get; set; } = new();
        public bool IsValid { get; set; } = true;
        public string ErrorMessage { get; set; }
    }

    private enum PatchType { Prefix, Postfix, Transpiler, Finalizer }

    // Utility Methods
    private static string ExtractStringFromAttributeArgument(AttributeArgumentSyntax arg) =>
        arg.Expression is LiteralExpressionSyntax literal 
            ? literal.Token.ValueText 
            : arg.Expression.ToString().Trim('"');

    private static bool IsHarmonyParameter(string paramName) =>
        paramName.StartsWith("__") || paramName == "instance" || paramName == "result";

    private static string GetHarmonyAttributeName(PatchType patchType) => patchType switch
    {
        PatchType.Prefix => "HarmonyPrefix",
        PatchType.Postfix => "HarmonyPostfix",
        PatchType.Transpiler => "HarmonyTranspiler",
        PatchType.Finalizer => "HarmonyFinalizer",
        _ => "HarmonyPrefix"
    };

    private static string GetNamespaceFromFullName(string fullName)
    {
        var lastDot = fullName.LastIndexOf('.');
        return lastDot > 0 ? fullName.Substring(0, lastDot) : string.Empty;
    }

    private static string GetSimpleClassName(string fullName)
    {
        var lastDot = fullName.LastIndexOf('.');
        return lastDot > 0 ? fullName.Substring(lastDot + 1) : fullName;
    }

    private static string RemovePatchSuffixFromMethodName(string methodName)
    {
        var suffixes = new[] { "_Prefix", "_Postfix", "_Transpiler", "_Finalizer", "_Patch", "_Hook", 
                              "Prefix", "Postfix", "Transpiler", "Finalizer", "Patch", "Hook" };
        
        foreach (var suffix in suffixes)
        {
            if (methodName.EndsWith(suffix, StringComparison.OrdinalIgnoreCase))
                return methodName.Substring(0, methodName.Length - suffix.Length);
        }
        
        return methodName;
    }

    private static bool IsSystemNamespace(string ns) =>
        ns.StartsWith("System") || ns.StartsWith("Microsoft") || 
        ns.StartsWith("UnityEngine") || ns.StartsWith("Il2Cpp");

    private static void ReportSuccess(SourceProductionContext context, MethodDeclarationSyntax method, PatchDefinition patch)
    {
        var descriptor = new DiagnosticDescriptor("AP001", "AutoPatch Success", 
            $"✓ Generated patch: {patch.OriginalMethodName} -> {patch.TargetSignature}", 
            "AutoPatch", DiagnosticSeverity.Info, true);
        context.ReportDiagnostic(Diagnostic.Create(descriptor, method.GetLocation()));
    }

    private static void ReportError(SourceProductionContext context, MethodDeclarationSyntax method, string errorMessage)
    {
        var descriptor = new DiagnosticDescriptor("AP002", "AutoPatch Error", 
            errorMessage, "AutoPatch", DiagnosticSeverity.Error, true);
        context.ReportDiagnostic(Diagnostic.Create(descriptor, method.GetLocation()));
    }
}