
// AutoPatch attribute definition (put this in a separate file)
// [System.AttributeUsage(System.AttributeTargets.Method)]
// public class AutoPatchAttribute : System.Attribute
// {
//     public string TargetType { get; set; }
//     public string TargetMethod { get; set; }
//     
//     public AutoPatchAttribute() { }
//     
//     public AutoPatchAttribute(string targetType, string targetMethod)
//     {
//         TargetType = targetType;
//         TargetMethod = targetMethod;
//     }
//     
//     public AutoPatchAttribute(string fullTarget)
//     {
//         var parts = fullTarget.Split('.');
//         if (parts.Length >= 2)
//         {
//             TargetMethod = parts[parts.Length - 1];
//             TargetType = string.Join(".", parts.Take(parts.Length - 1));
//         }
//     }
// }

/*
=== USAGE EXAMPLES ===

// Super simple - auto-detection (patches auto-applied by MelonLoader!)
public static class MyPatches
{
    [AutoPatch] // Auto-detects: GameObject.SetActive (from __instance parameter)
    public static bool SetActivePrefix(UnityEngine.GameObject __instance, bool value)
    {
        MelonLogger.Msg($"Setting {__instance.name} active: {value}");
        return true; // Continue with original
    }

    [AutoPatch] // Auto-detects: postfix for CalculateScore (void return type)
    public static void CalculateScorePostfix(ref int __result)
    {
        __result *= 2; // Double the score!
    }
}

// Explicit targeting
public static class ExplicitPatches
{
    [AutoPatch("UnityEngine.Transform", "Translate")]
    public static bool TranslatePrefix(UnityEngine.Transform __instance, Vector3 translation)
    {
        MelonLogger.Msg($"Translating {__instance.name} by {translation}");
        return true;
    }
    
    [AutoPatch("MyGame.Player.TakeDamage")]
    public static void TakeDamagePostfix(ref float __result, float damage)
    {
        MelonLogger.Msg($"Player took {damage} damage, health now: {__result}");
    }
}

// In your mod's main class (OPTIONAL - patches auto-apply!):
public override void OnApplicationStart()
{
    // Just for logging what patches were generated:
    AutoPatch.Generated.AutoPatchApplier.LogPatchInfo();
    
    // Manual patching only if you need special control:
    // var harmony = new Harmony("com.yourmod.autopatch");
    // AutoPatch.Generated.AutoPatchApplier.ManuallyApplyPatches(harmony);
}
*/