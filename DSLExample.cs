/*
# AutoPatch DSL Configuration File
# Define your patches here without cluttering your code with attributes

# Basic syntax: TargetType.MethodName -> PatchType [-> EventName]
# PatchType can be: Prefix, Postfix, Transpiler, Finalizer

# Example patches for MinimapFogOfWar
MinimapFogOfWar.UpdateFogOfWar -> Postfix
MinimapFogOfWar.ClearFog -> Prefix -> OnClearFogRequested
MinimapFogOfWar.RevealArea -> Postfix -> OnAreaRevealed

# You can also specify parameter types for overloaded methods
Player.TakeDamage(float) -> Prefix -> OnPlayerDamage
Player.TakeDamage(float, DamageType) -> Prefix -> OnPlayerDamageWithType

# Multiple patches for the same method
Enemy.Die -> Prefix -> OnEnemyDying
Enemy.Die -> Postfix -> OnEnemyDied

# Transpiler example
GameManager.Update -> Transpiler -> OnGameUpdateTranspile

# Comments and empty lines are ignored
# You can organize patches by feature:

## Movement System
PlayerController.Move -> Prefix
PlayerController.Jump -> Postfix -> OnPlayerJumped

## Combat System  
WeaponSystem.Fire -> Prefix -> OnWeaponFire
WeaponSystem.Reload -> Postfix -> OnWeaponReloaded

## UI System
UIManager.ShowDialog -> Prefix -> OnDialogShowing
UIManager.HideDialog -> Postfix -> OnDialogHidden

# Advanced syntax with parameter filtering
# Only patch methods with specific parameter signatures
InventoryManager.AddItem(Item, int) -> Postfix -> OnItemAdded
InventoryManager.RemoveItem(string) -> Prefix -> OnItemRemoving

# Conditional patches (future feature)
# GameSettings.SetDifficulty -> Prefix -> OnDifficultyChange [when: Debug]
*/


// // Example showing the new DSL-based AutoPatch system
// using System;
// using MelonLoader;
//
// // Target classes to patch (these would be from your game/application)
// public class Player
// {
//     public void TakeDamage(float amount)
//     {
//         MelonLogger.Msg($"Player taking {amount} damage");
//     }
//     
//     public void Die()
//     {
//         MelonLogger.Msg("Player died");
//     }
//     
//     public bool CanMove()
//     {
//         MelonLogger.Msg("Checking if player can move");
//         return true;
//     }
// }
//
// public class Enemy
// {
//     public void Attack(Player target)
//     {
//         MelonLogger.Msg("Enemy attacking player");
//     }
//     
//     public void Die()
//     {
//         MelonLogger.Msg("Enemy died");
//     }
// }
//
// // Instead of writing patch methods with attributes, you now:
// // 1. Define patches in patches.autopatch file
// // 2. Subscribe to the generated events
//
// public class DSLPatchExample
// {
//     public static void InitializePatches()
//     {
//         // The generator reads patches.autopatch and creates events like:
//         // - OnPlayerDamage (from: Player.TakeDamage -> Prefix -> OnPlayerDamage)
//         // - OnPlayerDied (from: Player.Die -> Postfix -> OnPlayerDied)
//         // - OnEnemyAttack (from: Enemy.Attack -> Prefix -> OnEnemyAttack)
//         
//         // Subscribe to the generated events
//         AutoPatch.Generated.Generated_DSLGenerated_Patches.OnPlayerDamage += (instance, amount) =>
//         {
//             MelonLogger.Msg($"[DSL Event] Player about to take {amount} damage!");
//             
//             if (amount > 100)
//             {
//                 MelonLogger.Msg("[DSL Event] Blocking massive damage!");
//                 return false; // Cancel the original method
//             }
//             
//             return true; // Allow original method to run
//         };
//         
//         AutoPatch.Generated.Generated_DSLGenerated_Patches.OnPlayerDied += (instance) =>
//         {
//             MelonLogger.Msg("[DSL Event] Player died! Triggering respawn...");
//             // Custom logic here
//         };
//         
//         AutoPatch.Generated.Generated_DSLGenerated_Patches.OnEnemyAttack += (instance, target) =>
//         {
//             MelonLogger.Msg($"[DSL Event] Enemy attacking! Applying difficulty modifier...");
//             return true; // Allow attack
//         };
//         
//         // Multiple handlers for the same event
//         AutoPatch.Generated.Generated_DSLGenerated_Patches.OnPlayerDied += (instance) =>
//         {
//             MelonLogger.Msg("[DSL Event] Second handler - updating statistics...");
//         };
//     }
// }
//
// /*
// Your patches.autopatch file would contain:
//
// # Player patches
// Player.TakeDamage -> Prefix -> OnPlayerDamage
// Player.Die -> Postfix -> OnPlayerDied
//
// # Enemy patches  
// Enemy.Attack -> Prefix -> OnEnemyAttack
// Enemy.Die -> Postfix -> OnEnemyDied
//
// # Movement system
// Player.CanMove -> Prefix -> OnPlayerCanMove
//
// */
//
// // Benefits of DSL approach:
// // ✅ No attributes cluttering your code
// // ✅ Centralized patch configuration
// // ✅ Easy to see all patches at a glance
// // ✅ Can be version controlled separately
// // ✅ Non-programmers can modify patch configuration
// // ✅ Still generates efficient event-based system
// // ✅ Multiple subscribers per patch
// // ✅ Runtime subscription/unsubscription
